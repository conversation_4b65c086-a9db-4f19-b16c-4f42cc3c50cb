import { Metadata } from 'next';
import ShopPage from '@/components/shop/ShopPage';
import ErrorBoundary from '@/components/shop/ErrorBoundary';

export const metadata: Metadata = {
  title: 'Shop - Mega Mall | Premium Products & Best Deals',
  description: 'Discover our extensive collection of premium products at unbeatable prices. Shop electronics, fashion, home goods, and more with fast shipping and excellent customer service.',
  keywords: 'shop, products, electronics, fashion, home goods, deals, online shopping, mega mall',
  openGraph: {
    title: 'Shop - Mega Mall | Premium Products & Best Deals',
    description: 'Discover our extensive collection of premium products at unbeatable prices.',
    type: 'website',
    url: '/shop',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Shop - Mega Mall | Premium Products & Best Deals',
    description: 'Discover our extensive collection of premium products at unbeatable prices.',
  },
};

export default function Shop() {
  return (
    <ErrorBoundary>
      <ShopPage />
    </ErrorBoundary>
  );
}
